# API Assistente Virtual IA

Este projeto é uma API com rotas utilizadas pela nossa assistente virtual IA para auxiliar os técnicos de campo.

## Rotas Disponíveis

As rotas estão definidas em `src/routes.js` e implementadas no controller `src/controllers/technician.js`.

### Rotas GET

- `/technician/provision-status/:macOrSerial`  
  Retorna informações sobre falha de provisionamento para o equipamento com o número de série ou MAC informado.

- `/technician/onu-signal/:macOrSerial`  
  Retorna informações de sinal (TX, RX, uptime) para o equipamento com o número de série ou MAC informado.

- `/technician/plan-info/:cpfCnpjOrIdContrato`  
  Retorna informações do plano associado ao CPF, CNPJ ou ID do contrato informado.

- `/technician/coverage/:lat/:lng`  
  Retorna informações de cobertura na localização geográfica informada (latitude e longitude).

- `/technician/coverage/:address`  
  Retorna informações de cobertura para o endereço informado.

### Rotas POST

- `/technician/operation-mode/:macOrSerial/:operationMode`  
  Recebe dados para alteração do modo de operação de um equipamento. A resposta é enviada após um tempo de espera de 4 minutos.

- `/technician/enable-mesh/:macOrSerial`  
  Recebe dados para ativar configuração de rede Mesh em um equipamento. A resposta é enviada após 3 segundos.

## Como Usar

1. **Instalação**  
   Clone o repositório e instale as dependências:
   ```bash
   npm install
   ```

2. **Executar a API**  
   Inicie o servidor:
   ```bash
   npm start
   ```

3. **Fazer Requisições**  
   Utilize um cliente HTTP (como Postman) para fazer requisições às rotas da API.

## Licença

Este projeto é de uso interno e não possui uma licença pública.

const { knex_main, knex_noc } = require('../services/knex.js')

// -----------------------------------------------------------------

async function writeRequestLog(req, options) {

	const requesterIp = req && req.connection ? req.connection.remoteAddress : null

	const defaultReq = {
		requestId: null,
		method: null,
		url: null,
		body: null,
		responseData: {},
		fingerprint: null
	}
	req = { ...defaultReq, ...req }

	req.fingerprint = req.body && req.body.fingerprint ? req.body.fingerprint : (req.user && req.user.fingerprint ? req.user.fingerprint : null)

	const defaultOptions = {
		action: null,
		message: null,
		error: req?.responseData?.status >= 400,
		error_code: null,
		error_message: null,
		error_stack: null,
	}
	options = { ...defaultOptions, ...options }

	const insert = await knex_main('requests_log').insert({
		action: options.action,
		message: options.message,
		requester_ip: requesterIp,
		requester_fingerprint: req.fingerprint,
		request_id: req.requestId,
		method: req.method,
		endpoint: req.url,
		request_body: JSON.stringify(req.body),
		response_status: req.responseData.status ?? null,
		response_body: req.responseData.body ?? null,
		response_headers: req.responseData.headers ?? null,
		error: options.error,
		error_code: options.error_code,
		error_message: options.error_message,
		error_stack: options.error_stack,
	})

	return insert
}

function isJson(str) {
	try {
		JSON.parse(str);
	} catch (e) {
		return false;
	}
	return true;
}

function getJson(variable) {
	return typeof variable === 'object' && variable !== null ? variable : JSON.parse(variable)
}

function formatMac(mac) {
	// Remove all non-alphanumeric chars
	mac = mac.replace(/[^A-Fa-f0-9]/g, "").trim();
	// Put ":" after every pair of chars
	mac = mac.replace(/(.{2})/g, "$1:");
	// Remove last ":"
	mac = mac.slice(0, -1);
	// To upper case
	mac = mac.toUpperCase();

	if (mac.length !== 17)
		return false;

	return mac;
}

function isMac(mac) {
	// Remove todos os caracteres não alfanuméricos
	mac = mac.replace(/[^A-Fa-f0-9]/g, "").trim();

	// Verifica se a string contém apenas caracteres hexadecimais
	if (!/^[A-Fa-f0-9]+$/.test(mac)) {
		return false;
	}

	// Verifica se a string tem a quantidade correta de caracteres (12)
	if (mac.length !== 12) {
		return false;
	}

	return true;
}

function isObject(value) {
	return typeof value === "object";
}

function formatDocument(cpfCnpj) {
	// Remove tudo que não for dígito
	const numeros = cpfCnpj.replace(/\D/g, "");

	// Checa se tem 11 dígitos (CPF)
	if (numeros.length === 11) {
		return numeros.replace(/(\d{3})(\d{3})(\d{3})(\d{2})/, "$1.$2.$3-$4");
	}
	// Checa se tem 14 dígitos (CNPJ)
	else if (numeros.length === 14) {
		return numeros.replace(
			/(\d{2})(\d{3})(\d{3})(\d{4})(\d{2})/,
			"$1.$2.$3/$4-$5"
		);
	}
	// Caso contrário, retorna false
	else {
		return false;
	}
}

function dateDmy(date, separator = '/') {
	if (date instanceof Date) {
		const day = date.getUTCDate().toString().padStart(2, "0");
		const month = (date.getUTCMonth() + 1).toString().padStart(2, "0");
		const year = date.getUTCFullYear();

		return `${day}${separator}${month}${separator}${year}`;
	}
	else if (typeof date === "string") {
		const regex = /^\d{4}-\d{2}-\d{2}$/;
		if (regex.test(date)) {
			const [year, month, day] = date.split("-");
			return `${day}${separator}${month}${separator}${year}`;
		} else {
			return null;
		}
	}
	else if (typeof date === "number") {
		const dateObject = new Date(date);
		const day = dateObject.getUTCDate().toString().padStart(2, "0");
		const month = (dateObject.getUTCMonth() + 1).toString().padStart(2, "0");
		const year = dateObject.getUTCFullYear();

		return `${day}${separator}${month}${separator}${year}`;
	}
	else {
		return null;
	}
}

function dateYmd(date) {
	if (date instanceof Date) {
		const day = date.getUTCDate().toString().padStart(2, "0");
		const month = (date.getUTCMonth() + 1).toString().padStart(2, "0");
		const year = date.getUTCFullYear();

		return `${year}-${month}-${day}`;
	}
	else if (typeof date === "string") {
		const regex = /^\d{2}\/\d{2}\/\d{4}$/;
		if (regex.test(date)) {
			const [day, month, year] = date.split("/");
			return `${year}-${month}-${day}`;
		} else {
			return null;
		}
	}
	else if (typeof date === "number") {
		const dateObject = new Date(date);
		const day = dateObject.getUTCDate().toString().padStart(2, "0");
		const month = (dateObject.getUTCMonth() + 1).toString().padStart(2, "0");
		const year = dateObject.getUTCFullYear();

		return `${year}-${month}-${day}`;
	}
	else {
		return null;
	}
}

function formatDate(date) {
	const day = date.getDate().toString().padStart(2, "0");
	const month = (date.getMonth() + 1).toString().padStart(2, "0"); // Mês começa a partir do zero, então adicionamos 1.
	const year = date.getFullYear();
	const hours = date.getHours().toString().padStart(2, "0");
	const minutes = date.getMinutes().toString().padStart(2, "0");
	const seconds = date.getSeconds().toString().padStart(2, "0");

	return `${day}/${month}/${year} ${hours}:${minutes}:${seconds}`;
}

async function getCoverage(lat, lng) {
	const sql = `
		WITH resultados AS (
			SELECT
			CASE
				WHEN hs.tipohost = 'Cobertura Fibra Backbone' THEN 'fibra-backbone'
				WHEN hs.tipohost = 'Cobertura Fibra' THEN 'fibra'
				WHEN hs.tipohost = 'Radio AP 5G - ACL Radius' THEN 'radio'
			END AS cobertura,
			p.cidade_pop AS cidade,
			CASE
				WHEN hs.tipohost = 'Cobertura Fibra Backbone' THEN 1
				WHEN hs.tipohost = 'Cobertura Fibra' THEN 2
				WHEN hs.tipohost = 'Radio AP 5G - ACL Radius' THEN 3
			END AS prioridade
			FROM geo.areas g
			INNER JOIN hosts_servicos hs ON (hs.id = g.servico_id AND hs.ativo = 1)
			INNER JOIN hosts h ON hs.host = h.host
			INNER JOIN pop p ON p.pop = h.pop
			WHERE ST_Distance_Sphere(g.area, ST_GeometryFromText('POINT(${lng} ${lat})', 4326)) <= 10
			AND hs.tipohost IN ('Cobertura Fibra', 'Radio AP 5G - ACL Radius', 'Cobertura Fibra Backbone')
		)
		SELECT cobertura, cidade
		FROM resultados
		ORDER BY prioridade
		LIMIT 1;
    `;

	const result = await knex_noc
		.raw(sql)

	return result?.rows?.length > 0 ? result.rows[0] : 'Sem cobertura'
}

// -------------------------------

exports.isJson = isJson
exports.getJson = getJson
exports.formatMac = formatMac
exports.isMac = isMac
exports.formatDocument = formatDocument
exports.isObject = isObject
exports.dateDmy = dateDmy
exports.dateYmd = dateYmd
exports.formatDate = formatDate
exports.writeRequestLog = writeRequestLog
exports.getCoverage = getCoverage
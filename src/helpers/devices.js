const { knex_noc } = require('../services/knex')
const { axiosProvision } = require('../services/axios')
const { formatDate } = require('./common')

async function getProvisionInfo(serial, limit = 2) {
    const provisionInfo = await knex_noc('authservice.provisions as p')
        .select('status', 'enqueued_at', 'ended_at', 'exc_info', 'username', 'olt', 'slot', 'pon', 'olt_model')
        .where('serial', serial)
        .orderBy('enqueued_at', 'desc')
        .limit(limit);

    const provisions = [];
    for (const provision of provisionInfo) {
        const statusTranslation = {
            finished: 'finalizado',
            canceled: 'cancelado',
            queued: 'executando'
        };

        const provisionData = {
            iniciado_em: formatDate(provision.enqueued_at),
            finalizado_em: formatDate(provision.ended_at),
            status: statusTranslation[provision.status] || provision.status,
            erro: provision.exc_info,
            login: provision.username,
            olt: {
                nome: provision.olt,
                placa: provision.slot,
                porta: provision.pon,
                modelo: provision.olt_model
            }
        };
        provisions.push(provisionData);
    }

    return provisions;
}

async function getDeviceSignal(macOrSerial) {
    const url = `/onu/signal/${macOrSerial}`
    const deviceSignal = await axiosProvision.get(url)

    if (!deviceSignal?.data?.signal)
        throw new Error('Ocorreu um erro ao consultar o sinal da ONU')

    return deviceSignal.data.signal
}

module.exports = {
    getProvisionInfo,
    getDeviceSignal,
}
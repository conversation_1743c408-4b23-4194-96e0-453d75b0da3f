const { knex_main } = require('../services/knex')
const { formatDate } = require("./common");
const tokens = require("../token.json");

// ----------- REQUEST/RESPONSE MIDDLEWARE LOGGERS --------------------

let requestId = 0;

async function requestLoggerMiddleware(req, res, next) {
    // Não registra requisições OPTIONS ou de polling
    // if (req.body.polling || req.method === 'OPTIONS') {
    //   next();
    //   return
    // }

    // if (!requestId) requestId = await getRequestId();

    // Ignora os componentes da impressão digital, nos endpoints não-especificados para armazenagem
    // if (!FINGERPRINT_SECURED_ENDPOINTS.includes(req.url))
    //     req.body.fingerprintComponents = null;

    req.requestId = requestId++;
    req.request_endpoint = "[" + req.method + "] " + req.url;

    // await writeRequestLog(req, {
    //     action: "REQUISICAO",
    //     message: "Requisição recebida",
    // });

    console.log(
        "[" + formatDate(new Date()) + "]",
        "[REQUEST #" + req.requestId + "]",
        "[" + req.method + "]",
        req.url,
        "[BODY: " + JSON.stringify(req.body) + "]"
    );

    next();
}

function responseLoggerMiddleware(req, res, next) {
    // Não registra requisições OPTIONS ou de polling
    // if (req.body.polling || req.method === 'OPTIONS') {
    //   next();
    //   return
    // }

    const originalSend = res.send.bind(res);

    res.send = function (responseBody) {
        const now = formatDate(new Date());
        const responseStatus = res.statusCode;

        const isError = !responseBody.success || false;
        const errorCode = isError ? (responseBody.code ? responseBody.code : null) : null
        const errorMessage = isError ? (responseBody.error ? responseBody.error : null) : null
        const errorStack = isError ? (responseBody.errorStack ? responseBody.errorStack : null) : null

        // Após coletar o stacktrace do erro, remove ele do corpo da resposta, para evitar ser exibido ao client
        delete responseBody.errorStack;

        const responseBodyJson = JSON.stringify(responseBody);
        const responseHeaders = JSON.stringify(res.getHeaders());

        console.log(
            `[${now}]`,
            `[RESPONSE #${req.requestId}]`,
            `[STATUS: ${responseStatus}]`,
            `[BODY: "${responseBodyJson}"]`,
            `[HEADERS: ${responseHeaders}]`
        );

        req.responseData = {
            status: responseStatus,
            body: responseBodyJson,
            headers: responseHeaders,
        };

        // writeRequestLog(req, {
        //     action: "RESPOSTA",
        //     message: "Resposta da requisição",
        //     error: isError,
        //     error_code: errorCode,
        //     error_message: errorMessage,
        //     error_stack: errorStack,
        // });

        res.send = originalSend; // Set function back to avoid the 'double-send'
        return res.send(responseBody);
    };

    next();
}

const responseHandler = (req, res, next) => {
    // Função para enviar respostas de sucesso
    res.sendSuccess = (data = {}, jwtToken = null) => {

        // Caso o payload de resposta contiver "jwtToken" e o jwtToken não foi passado como parâmetro, extrai o token do payload
        if (data.jwtToken && !jwtToken)
            jwtToken = data.jwtToken

        // Caso o payload de resposta contiver "jwtToken", remove o token do payload
        if (data.jwtToken)
            delete data.jwtToken

        const responsePayload = {
            success: true,
            data: data,
        }

        if (jwtToken)
            responsePayload.jwtToken = jwtToken;

        res.send(responsePayload);
    };

    // Função para enviar respostas de erro
    res.sendError = (options = {}) => {
        const { message = '', code = null, error = null } = options;
        const errorStack = error ? error.stack : null

        if (errorStack)
            console.error('Erro:', errorStack)

        // Define o código de erro com base nas seguintes prioridades:
        // 1. options.code
        // 2. error.code
        // 3. Caso contrário, ERRO_INTERNO
        const errorCode = code ? code : (error && error.code ? error.code : 'ERRO_INTERNO')

        res.send({
            success: false,
            error: message,
            // code: errorCode,

            // errorStack não será enviado ao cliente, mas sim armazenado no banco de dados e removido da resposta pelo middleware "responseLoggerMiddleware", antes de ser enviada ao cliente
            errorStack: errorStack,
        });
    };

    next();
};

async function getRequestId() {
    const row = await knex_main("requests_log")
        .max("request_id as max_id")
        .first();

    let idRequisicao = 1;

    if (row) idRequisicao = row.max_id + 1;

    return idRequisicao;
}

async function authHandler(req, res, next) {
    const token = req.body.token

    console.log(req.headers)

    if(!token || !tokens.includes(token)){
        return res.send({
            msg: "O código de autenticação está incorreto, tente novamente."
        })
    }   
    next();
}



exports.responseHandler = responseHandler;
exports.requestLoggerMiddleware = requestLoggerMiddleware;
exports.responseLoggerMiddleware = responseLoggerMiddleware;
exports.authHandler = authHandler;
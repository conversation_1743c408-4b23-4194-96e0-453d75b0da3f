const { formatMac } = require('./common')
const { knex_ixc } = require('../services/knex')

async function getIdsContratos(cpfCnpj) {
    const idsContratos = await knex_ixc('cliente_contrato')
        .join('cliente', 'cliente_contrato.id_cliente', '=', 'cliente.id')
        .where('cliente.cnpj_cpf', cpfCnpj)
        .whereNotIn('status', ['D', 'I'])
        .pluck('cliente_contrato.id')

    return idsContratos.length > 0 ? idsContratos : false
}

async function getContratoProdutos(idContrato) {
    const result = await knex_ixc('cliente_contrato as cc')
        .leftJoin('vd_contratos_produtos as vcp', function() {
            this.on('vcp.id_contrato', '=', 'cc.id').orOn('vcp.id_vd_contrato', '=', 'cc.id_vd_contrato')
        })
        .where('cc.id', idContrato)
        .pluck('vcp.descricao')

    return result
}

async function getMacBySerial(serialFornecedor) {
    const result = await knex_ixc('patrimonio as p')
        .whereRaw('LOWER(p.serial_fornecedor) = LOWER(TRIM(?))', [serialFornecedor])
        .pluck('p.id_mac');

    return result.length > 0 ? formatMac(result[0]) : false
}

async function getSerialByMac(mac) {
    const result = await knex_ixc('patrimonio as p')
        .whereRaw("UPPER(REGEXP_REPLACE(p.id_mac, '[^A-Fa-f0-9]', '')) = UPPER(REGEXP_REPLACE(?, '[^A-Fa-f0-9]', ''))", [mac])
        .pluck('p.serial_fornecedor');

    return result.length > 0 ? result[0] : false;
}

async function getLoginByMac(mac) {
    const result = await knex_ixc('radusuarios as r')
        .whereRaw("UPPER(REGEXP_REPLACE(r.mac, '[^A-Fa-f0-9]', '')) = UPPER(REGEXP_REPLACE(?, '[^A-Fa-f0-9]', ''))", [mac])
        .pluck('r.login');

    return result.length > 0 ? result[0] : false
}

exports.getMacBySerial = getMacBySerial
exports.getIdsContratos = getIdsContratos
exports.getContratoProdutos = getContratoProdutos
exports.getLoginByMac = getLoginByMac
exports.getSerialByMac = getSerialByMac
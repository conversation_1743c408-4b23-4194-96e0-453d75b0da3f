const fs = require('fs')
const express = require('express')
module.exports = (server) => {
	const controllers = getControllers()

	// ------------ Routes for field technicians --------------
	const technicianRouter = express.Router()

	const { technician } = controllers
	
	technicianRouter.post('/provision-status/:macOrSerial', technician.getProvisionStatus)
	technicianRouter.post('/onu-signal/:macOrSerial', technician.getOnuSignal)
	technicianRouter.post('/plan-info/:cpfCnpjOrIdContrato', technician.getPlanInfo)
	technicianRouter.post('/coverage/coordinates/:lat/:lng', technician.getCoverageByCoordinates)
	technicianRouter.post('/coverage/:address', technician.getCoverageByAddress)
	technicianRouter.post('/operation-mode/:macOrSerial/:operationMode', technician.setOperationMode)
	technicianRouter.post('/enable-mesh/:macOrSerial', technician.enableMesh);
	technicianRouter.post('/mesh-status/:macOrSerial', technician.getMeshStatus);

	server.use('/technician', technicianRouter)
}

// ---------------------------------------

function getControllers() {
	const controllers_path = __dirname + '/controllers'

	const controllers = {}
	fs.readdirSync(controllers_path).forEach(function (file) {
		if (file.indexOf('.js') != -1) {
			controllers[file.split('.')[0]] = require(controllers_path + '/' + file)
		}
	})

	return controllers
}
const { DATABASES } = require('../config.json')
const util = require('util')

const knex_noc = require('knex')({
	client: 'pg',
	connection: {
		host: DATABASES.NOC2.HOST,
		user: DATABASES.NOC2.USER,
		password: DATABASES.NOC2.PASSWORD,
		database: DATABASES.NOC2.DATABASE
	}
});
exports.knex_noc = knex_noc

exports.knex_ixc = require('knex')({
	client: 'mysql',
	connection: {
		host: DATABASES.IXC.HOST,
		user: DATABASES.IXC.USER,
		password: DATABASES.IXC.PASSWORD,
		database: DATABASES.IXC.DATABASE
	}
});

exports.insertOrUpdate = (tableName, uniqueColumn, blogPosts) => {
	return knex.transaction(trx => {
		let queries = blogPosts.map(tuple =>
			trx.raw(util.format('%s ON CONFLICT (' + uniqueColumn + ') DO UPDATE SET %s',
				trx(tableName).insert(tuple).toString().toString(),
				trx(tableName).update(tuple).whereRaw(`${tableName}.${uniqueColumn} = '${tuple[uniqueColumn]}'`).toString().replace(/^update\s.*\sset\s/i, '')
			))
				.transacting(trx)
		);
		return Promise.all(queries).then(trx.commit).catch(trx.rollback);
	})
}
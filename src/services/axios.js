const { IXC_WEBSERVICE_URL, IXC_WEBSERVICE_TOKEN } = require('../config.json')
const { GENIE_API_URL, PROVISION_API_URL } = require('../config.json')
const https = require('https')

const axiosIxc = require('axios');
axiosIxc.defaults.timeout = 400000;
// axiosIxc.defaults.withCredentials = true;
axiosIxc.defaults.baseURL = IXC_WEBSERVICE_URL;
axiosIxc.defaults.headers.post['Content-Type'] = 'application/json';
axiosIxc.defaults.headers.common['Authorization'] = 'Basic ' + Buffer.from(IXC_WEBSERVICE_TOKEN).toString('base64')

axiosIxc.interceptors.response.use(response => {
	return response;
}, error => {
	if (!error.response) {
		// Handle API Offline Error
	}
	else if (error.response.status === 401) {
		//
	}
	return error;
})

// --------------------------------------

const axiosGenie = require('axios');
axiosGenie.defaults.timeout = 400000;
// axiosGenie.defaults.withCredentials = true;
axiosGenie.defaults.baseURL = GENIE_API_URL;
axiosGenie.defaults.headers.post['Content-Type'] = 'application/json';

axiosGenie.interceptors.response.use(response => {
	return response;
}, error => {
	if (!error.response) {
		// Handle API Offline Error
	}
	else if (error.response.status === 401) {
		//
	}
	return error;
})

// --------------------------------------

const axiosProvision = require('axios');
axiosProvision.defaults.timeout = 400000;
// axiosProvision.defaults.withCredentials = true;
axiosProvision.defaults.baseURL = PROVISION_API_URL;
axiosProvision.defaults.headers.post['Content-Type'] = 'application/json';
axiosProvision.defaults.headers['x-access-token'] = 'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpYXQiOjE2Nzg0NzQ3NTUsImV4cCI6MzMyMDM4NDc1NTV9.6hGxRvNmlYnjxOIDVZnX_uPmD3i9qqzbEqm4yH-dO-o';
axiosProvision.defaults.httpsAgent = new https.Agent({
    rejectUnauthorized: false,
}) // Disable SSL verification

axiosProvision.interceptors.response.use(response => {
	return response;
}, error => {
	if (!error.response) {
		// Handle API Offline Error
	}
	else if (error.response.status === 401) {
		//
	}
	return error;
})

module.exports = {
    axiosIxc,
	axiosGenie,
	axiosProvision,
}
const { SSL_CERTIFICATE_PATH, MODE, PORT: serverPort } = require('./config');
const express = require('express');
const fs = require('fs');

const {
	requestLoggerMiddleware,
	responseLoggerMiddleware,
	responseHandler,
	authHandler,
} = require('./helpers/middlewares.js')

// ------------------------------------------
if (MODE === 'PROD')
	process.env.NODE_ENV = 'production';
else if (MODE === 'DEV')
	process.env.NODE_ENV = 'development';
// ------------------------------------------

const server = express()

// Desativa cabeçalho padrão {"x-powered-by":"Express"}
server.disable('x-powered-by')

// Parse Request JSON body to req.body object
server.use(express.json())

// Middleware for request info logging
server.use(requestLoggerMiddleware)

// Middleware for request info logging
server.use(responseLoggerMiddleware)

// Middleware for response standardization
server.use(responseHandler)

//Middleware for authentication
server.use(authHandler)

// ----------------- ROUTING -------------------

require('./routes.js')(server)

/// ------------------- LISTENING -----------------------------
if (MODE === 'PROD') {
	// Modo PROD: HTTPS
	const https = require('https');
	const fs = require('fs');

	const sslOptions = {
		key: fs.readFileSync(`${SSL_CERTIFICATE_PATH}/privkey.pem`),
		cert: fs.readFileSync(`${SSL_CERTIFICATE_PATH}/fullchain.pem`),
		// ca: fs.readFileSync('caminho/para/ca_bundle.crt') // Opcional
	};

	// Cria o servidor HTTPS passando a instância do Express (server)
	https.createServer(sslOptions, server).listen(serverPort, () => {
		console.log(`API rodando em HTTPS (PROD) na porta ${serverPort}`);
	});
} else {
	// Modo DEV: HTTP simples
	server.listen(serverPort, () => {
		console.log(`API rodando em HTTP (DEV) na porta ${serverPort}`);
	});
}
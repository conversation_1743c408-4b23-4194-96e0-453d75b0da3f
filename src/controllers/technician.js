const { getIdsContratos, getContratoProdutos, getMacBySerial, getLoginByMac, getSerialByMac } = require('../helpers/ixc.js')
const { formatDocument, isMac, getCoverage } = require('../helpers/common.js')
const { axiosGenie } = require('../services/axios')
const { getProvisionInfo, getDeviceSignal } = require('../helpers/devices')

exports.getProvisionStatus = async (req, res) => {
	try {
		const { macOrSerial } = req.params
		let serial

		if (isMac(macOrSerial)) {
			serial = await getSerialByMac(macOrSerial);
			if (!serial)
				throw new Error('Este MAC não foi encontrado. Revise o MAC ou tente novamente através do serial da ONU.')
		}
		else {
			serial = macOrSerial
		}

		const provisionInfo = await getProvisionInfo(serial, 2) // Consulta os últimos 2 provisionamentos

		if (!provisionInfo || provisionInfo.length == 0)
			throw new Error('Não há provisionamentos desta ONU')

		return res.sendSuccess({
			provisionamentos: provisionInfo
		})
	}
	catch (error) {
		return res.sendError(error)
	}
}

exports.getOnuSignal = async (req, res) => {
	try {
		const { macOrSerial } = req.params

		const onuSignal = await getDeviceSignal(macOrSerial)
		
		if (!onuSignal)
			throw new Error('Não foram encontradas informações de sinal desta ONU.')

		const parsedSignal = {
			olt_rx_power: parseFloat(onuSignal.olt_rx_power),
			onu_rx_power: parseFloat(onuSignal.onu_rx_power),
			onu_tx_power: parseFloat(onuSignal.onu_tx_power)
		}

		// "minimum": sinal mínimo "aceitável"
		const signalInfo = {
			olt_rx_power: {
				name: 'sinal de recepção do OLT',
				minimum: -24,
			},
			onu_rx_power: {
				name: 'sinal de recepção da ONU',
				minimum: -24,
			},
			onu_tx_power: {
				name: 'sinal de transmissão da ONU',
				minimum: 2,
			},
		}

		let signalStatus = []
		for (const [key, { name, minimum }] of Object.entries(signalInfo)) {
			if (parsedSignal[key] < minimum) {
				signalStatus.push(`O ${name} parece estar fora do padrão`);
			}
		}

		if (signalStatus.length === 0) {
			signalStatus.push('O sinal está dentro do padrão');
		}

		return res.sendSuccess({
			signal: parsedSignal,
			status: signalStatus,
		})
	}
	catch (error) {
		return res.sendError(error)
	}
}

exports.getCoverageByCoordinates = async (req, res) => {
	try {
		const { lat, lng } = req.params

		coverage = await getCoverage(lat, lng)

		return res.sendSuccess(coverage)
	}
	catch (error) {
		return res.sendError(error)
	}
}

exports.getCoverageByAddress = async (req, res) => {
	try {
		const { address } = req.params

		

		res.send({
			disponibilidade: "sim",
			lat: req.params.lat,
			lng: req.params.lng
		})
	}
	catch (error) {
	}
}

exports.getPlanInfo = async (req, res) => {
	try {
		const { cpfCnpjOrIdContrato } = req.params
		const cpfCnpj = formatDocument(cpfCnpjOrIdContrato)
		let idContrato

		if (cpfCnpj) {
			const idsContratos = await getIdsContratos(cpfCnpj)
			if (!idsContratos) {
				return res.status(404).send({ message: 'Este CPF/CNPJ não possui contratos' })
			}
			if (idsContratos.length > 1) {
				return res.sendError({
					message: `Este cliente possui mais de um contrato. Por favor, informe o ID do contrato do qual que deseja consultar as informações do plano. Os IDs disponíveis são: ${JSON.stringify(idsContratos)}`
				})
			}
			idContrato = idsContratos[0]
		}
		else {
			idContrato = cpfCnpjOrIdContrato
		}

		const planInfo = await getContratoProdutos(idContrato);

		if (!planInfo) {
			return res.sendError({
				message: 'O cliente foi encontrado mas não foi possível consultar as informações do plano. Contacte o COR.'
			})
		}

		return res.sendSuccess({
			pacotes: planInfo
		})
	}
	catch (error) {
		return res.sendError(error)
	}
}

exports.setOperationMode = async (req, res) => {
	try {
		const { operationMode, macOrSerial } = req.params
		let mac

		if (isMac(macOrSerial)) {
			mac = macOrSerial
		}
		else {
			mac = await getMacBySerial(macOrSerial);
			if (!mac)
				throw new Error('Este serial não foi encontrado. Revise o serial ou tente novamente através do MAC da ONU.')
		}

		const genieUrl = `/device/${operationMode}-mode/${mac}`
		const setOperation = await axiosGenie.post(genieUrl)

		if (setOperation?.data?.status !== 'success')
			throw new Error(`Ocorreu um erro ao tentar alterar o modo de operação para ${operationMode}: ` + setOperation?.data?.message ? setOperation.data.message : 'erro desconhecido')

		return res.sendSuccess(`A mudança de operação da ONU ${macOrSerial} para ${operationMode} foi concluída.`)
	}
	catch (error) {
		return res.sendError(error)
	}
}

exports.enableMesh = async (req, res) => {
	try {
		const { macOrSerial } = req.params
		let mac

		if (isMac(macOrSerial)) {
			mac = macOrSerial
		}
		else {
			mac = await getMacBySerial(macOrSerial);
			if (!mac)
				throw new Error('Este serial não foi encontrado. Revise o serial ou tente novamente através do MAC da ONU.')
		}

		const login = await getLoginByMac(mac);

		const enableMeshResponse = await axiosGenie.post('/mesh/enable', {
			login,
			identifier: mac
		})

		if (enableMeshResponse?.data?.status !== 'success')
			throw new Error(`Ocorreu um erro ao tentar ativar o Mesh na ONU: ` + enableMeshResponse?.data?.message ? enableMeshResponse.data.message : 'erro desconhecido')

		return res.sendSuccess(`O mesh foi ativado na ONU ${macOrSerial}.`)
	}
	catch (error) {
		return res.sendError(error)
	}
}

exports.getMeshStatus = async (req, res) => {
	try {
		const { macOrSerial } = req.params
		let mac

		if (isMac(macOrSerial)) {
			mac = macOrSerial
		}
		else {
			mac = await getMacBySerial(macOrSerial);
			if (!mac)
				throw new Error('Este serial não foi encontrado. Revise o serial ou tente novamente através do MAC da ONU.')
		}

		const genieUrl = `/mesh/status/${mac}`
		const meshStatus = await axiosGenie.get(genieUrl)

		if (!meshStatus?.data?.status)
			throw new Error(`Ocorreu um erro ao consultar o status do Mesh na ONU: ` + meshStatus?.data?.message ? meshStatus.data.message : 'erro desconhecido')

		return res.sendSuccess(meshStatus.data)
	}
	catch (error) {
		return res.sendError(error)
	}
}